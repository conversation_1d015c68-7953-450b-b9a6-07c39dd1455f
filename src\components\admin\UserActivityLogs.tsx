'use client';

import React, { useState, useEffect, use<PERSON><PERSON>back, useRef, useImperativeHandle } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import {
  Clock,
  RefreshCw,
  LogIn,
  Settings,
  MessageSquare,
  AlertCircle,
  UserPlus,
  Server
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { useToastHelpers } from '@/lib/ToastContext';

interface ActivityLog {
  id: string;
  userId: string;
  type: string;
  action: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  userName: string;
  userEmail?: string;
  user?: {
    name: string;
    email: string;
    profileImage?: string;
  };
}

interface UserActivityLogsProps {
  userId?: string; // Optional - if provided, shows logs for a specific user
  limit?: number; // Optional - number of logs to fetch (default: 50)
  autoRefresh?: boolean; // Optional - whether to auto-refresh logs
  refreshInterval?: number; // Optional - refresh interval in ms (default: 30000)
  onError?: (message: string) => void; // Optional - callback for error handling by parent
}

// Create a forwardRef version of the component to expose methods to parent
const UserActivityLogs = React.forwardRef(({
  userId,
  limit = 50,
  autoRefresh = true,
  refreshInterval = 30000,
  onError
}: UserActivityLogsProps, ref: React.ForwardedRef<{ fetchLogs: (silent: boolean) => Promise<void> }>): JSX.Element => {
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userCache, setUserCache] = useState<Record<string, { name: string; profileImage?: string }>>({});
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const toast = useToastHelpers();

  // Get auth context
  const { user: authUser, isAuthenticated } = useAuth();

  // Track if component is mounted
  const isMounted = useRef(true);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Track last successful fetch time to avoid unnecessary refreshes
  const lastSuccessfulFetch = useRef<number>(0);

  // Track API responses for caching
  const apiCacheRef = useRef<{ logs: ActivityLog[]; timestamp: number } | null>(null);

  // Minimum time between refreshes (in milliseconds)
  const MIN_REFRESH_INTERVAL = 120000; // 2 minutes (increased from 30 seconds)

  // Cache expiration time (in milliseconds)
  const CACHE_EXPIRATION = 300000; // 5 minutes (increased from 60 seconds)

  // Safe state update helpers to prevent updates after unmounting
  const safeSetLogs = useCallback((newLogs: ActivityLog[]) => {
    if (isMounted.current) {
      setLogs(newLogs);
    }
  }, []);

  const safeSetLoading = useCallback((loading: boolean) => {
    if (isMounted.current) {
      setIsLoading(loading);
    }
  }, []);

  const safeSetError = useCallback((errorMsg: string | null) => {
    if (isMounted.current) {
      setError(errorMsg);
    }
  }, []);

  const safeSetUserCache = useCallback((cacheUpdater: (prev: Record<string, { name: string; profileImage?: string }>) => Record<string, { name: string; profileImage?: string }>) => {
    if (isMounted.current) {
      setUserCache(cacheUpdater);
    }
  }, []);

  const safeSetLastRefresh = useCallback((date: Date) => {
    if (isMounted.current) {
      setLastRefresh(date);
    }
  }, []);

  // Fetch user info function
  const fetchUserInfo = useCallback(async () => {
    if (userId) return; // Skip if we're only showing logs for a specific user

    const uniqueUserIds = new Set<string>();

    // Collect unique user IDs that aren't in our cache yet
    logs.forEach(log => {
      if (log.userId && !userCache[log.userId]) {
        uniqueUserIds.add(log.userId);
      }
    });

    // If no new users to fetch, return early
    if (uniqueUserIds.size === 0) return;

    // Fetch user info for each unique ID
    for (const id of uniqueUserIds) {
      try {
        // Get userId from auth context or localStorage
        const localUserId = authUser?.id || localStorage.getItem('userId');

        // Log authentication status
        if (!localUserId) {
          console.warn('No userId found for logging. Proceeding with request using cookies only.');
        }

        const response = await fetch(`/api/admin/users/${id}`, {
          credentials: 'include', // Include cookies for authentication
          headers: {
            'Cache-Control': 'no-cache'
          }
        });

        if (response.ok) {
          const userData = await response.json();
          safeSetUserCache(prev => ({
            ...prev,
            [id]: {
              name: userData.name,
              profileImage: userData.profileImage
            }
          }));
        }
      } catch (error) {
        console.error(`Error fetching user info for ID ${id}:`, error);
      }
    }
  }, [logs, userCache, userId, safeSetUserCache, authUser]);

  // Track fetch attempts to prevent excessive retries
  const fetchAttemptsRef = useRef<number>(0);
  const MAX_FETCH_ATTEMPTS = 3;

  // Global request lock to prevent multiple simultaneous requests
  const isRequestInProgressRef = useRef(false);

  // Track if rate limiting is active
  const rateLimitActiveRef = useRef(false);

  // Check for circuit breaker on component mount
  useEffect(() => {
    try {
      // Clear any emergency stop that might be active
      sessionStorage.removeItem('adminActivityEmergencyStop');

      // Check for circuit breaker
      const circuitOpen = sessionStorage.getItem('circuitBreakerOpen') === 'true';
      if (circuitOpen) {
        console.warn('Circuit breaker is active. API calls will be rate limited.');
        rateLimitActiveRef.current = true;
      }
    } catch (e) {
      // Ignore storage errors
    }
  }, []);

  // Global cache for activity logs to share between instances
  const globalCacheKey = `activity_logs_${userId || 'all'}_${limit}`;

  // Try to get cached data from sessionStorage on initial load
  useEffect(() => {
    try {
      // Log authentication status
      if (!isAuthenticated) {
        console.log('User not authenticated in auth context, but continuing with cookies');
      }

      // Then try to get cached data
      const cachedData = sessionStorage.getItem(globalCacheKey);
      if (cachedData) {
        const { logs, timestamp } = JSON.parse(cachedData);
        const cacheAge = Date.now() - timestamp;

        // Only use cache if it's not too old
        if (cacheAge < CACHE_EXPIRATION) {
          console.log(`Using cached activity logs from sessionStorage (${Math.round(cacheAge / 1000)}s old)`);
          safeSetLogs(logs);
          safeSetLastRefresh(new Date(timestamp));
          apiCacheRef.current = { logs, timestamp };
          lastSuccessfulFetch.current = timestamp;
        }
      }
    } catch (error) {
      console.error('Error reading from sessionStorage:', error);
      // Don't set error here, let the fetchLogs handle errors
    }
  }, [globalCacheKey, safeSetLogs, safeSetLastRefresh, safeSetError, safeSetLoading, isAuthenticated]);

  // Process response data and update state
  const processResponseData = useCallback((logsArray: ActivityLog[]) => {
    // Update logs state
    safeSetLogs(logsArray);
    safeSetLastRefresh(new Date());

    // Extract user information from logs and update the cache
    logsArray.forEach((log: ActivityLog) => {
      // Create an object to store in cache
      const cacheEntry = {
        name: 'Unknown User',
        profileImage: ''
      };

      // Use userName from API if available
      if (log.userName && log.userName !== 'Unknown') {
        cacheEntry.name = log.userName;
        // Optional chaining for profileImage
        if (log.user?.profileImage) {
          cacheEntry.profileImage = log.user.profileImage;
        }
      }
      // Use user object if available
      else if (log.user?.name) {
        cacheEntry.name = log.user.name;
        if (log.user.profileImage) {
          cacheEntry.profileImage = log.user.profileImage;
        }
      }

      // Only update cache if we have a valid name that's not "Unknown User"
      if (cacheEntry.name !== 'Unknown User') {
        safeSetUserCache(prev => ({
          ...prev,
          [log.userId]: cacheEntry
        }));
      }
    });
  }, [safeSetLogs, safeSetLastRefresh, safeSetUserCache]);

  // Fetch activity logs with improved caching and throttling
  const fetchLogs = useCallback(async (silent = false) => {
    try {
      // Check if rate limiting is active
      if (rateLimitActiveRef.current) {
        console.warn('Rate limiting is active, API calls are limited');
        if (!silent) {
          safeSetError('API calls are rate limited. Please wait before trying again.');
          safeSetLoading(false);
        }
        return;
      }

      // Check if a request is already in progress
      if (isRequestInProgressRef.current) {
        console.warn('Request already in progress, skipping duplicate request');
        if (!silent) {
          toast.info('Request in Progress', 'Please wait for the current request to complete.');
          safeSetLoading(false);
        }
        return;
      }

      // Set request lock
      isRequestInProgressRef.current = true;

      // Don't show loading state for silent refreshes
      if (!silent) {
        safeSetLoading(true);
      }
      safeSetError(null);

      const now = Date.now();

      // Check if we can use cached data (for both silent and manual refreshes)
      if (apiCacheRef.current && (now - apiCacheRef.current.timestamp < CACHE_EXPIRATION)) {
        console.log(`Using cached activity logs data (${Math.round((now - apiCacheRef.current.timestamp) / 1000)}s old)`);
        processResponseData(apiCacheRef.current.logs);
        if (!silent) safeSetLoading(false);

        // For manual refreshes, still update the last refresh time UI
        if (!silent) {
          safeSetLastRefresh(new Date());
        }
        return;
      }

      // Check if we should skip this refresh due to recent successful fetch
      if (now - lastSuccessfulFetch.current < MIN_REFRESH_INTERVAL) {
        console.log(`Skipping activity logs refresh - too soon since last fetch (${Math.round((now - lastSuccessfulFetch.current) / 1000)}s ago)`);
        if (!silent) safeSetLoading(false);
        return;
      }

      // Get userId from auth context or localStorage for logging purposes
      const localUserId = authUser?.id || localStorage.getItem('userId');

      // Log authentication status
      if (!localUserId) {
        console.warn('No userId found in auth context or localStorage. Proceeding with request using cookies only.');
      } else {
        console.log(`Using userId for logging: ${localUserId}`);
      }

      // Add cache busting parameter only for manual refreshes
      const cacheBuster = silent ? '' : `&_t=${Date.now()}`;

      // Construct URL with optional userId parameter and limit
      const url = userId
        ? `/api/admin/users/${userId}/activity?limit=${limit}${cacheBuster}`
        : `/api/admin/activity?limit=${limit}${cacheBuster}`;

      // Increment fetch attempts
      fetchAttemptsRef.current++;

      // Log fetch attempt with counter to track frequency
      console.log(`Fetching activity logs (attempt ${fetchAttemptsRef.current}): ${url}`);

      // Use AbortController to cancel the request if it takes too long
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 20000); // 20 second timeout (increased from 15s)

      try {
        console.log(`Making fetch request to ${url} with userId: ${localUserId}`);

        // Create a more robust fetch request - use cookies for authentication
        const response = await fetch(url, {
          method: 'GET',
          credentials: 'include', // Include cookies for authentication
          headers: {
            'Content-Type': 'application/json',
            // Use more moderate cache directives to ensure fresh data
            'Cache-Control': silent ? 'max-age=60' : 'no-cache',
            'Pragma': silent ? '' : 'no-cache'
          },
          signal: controller.signal,
          // Add a cache: 'no-store' option for manual refreshes
          cache: silent ? 'default' : 'no-store'
        });

        // Clear the timeout since the request completed
        clearTimeout(timeoutId);

        if (!response.ok) {
          // Handle authentication errors specifically
          if (response.status === 401) {
            const errorMsg = 'Authentication session expired. Please refresh the page or sign in again.';
            safeSetError(errorMsg);
            if (onError && !silent) onError(errorMsg);
            if (!silent) {
              toast.error('Authentication Error', 'Your session has expired. Please sign in again.');
            }
            safeSetLoading(false);
            return;
          }

          // Handle rate limiting errors
          if (response.status === 429) {
            // Get retry-after header and circuit breaker status
            const retryAfter = response.headers.get('Retry-After');
            const retrySeconds = retryAfter ? parseInt(retryAfter, 10) : 60;
            const circuitBreakerStatus = response.headers.get('X-Circuit-Breaker');
            const isCircuitOpen = circuitBreakerStatus === 'open';

            // Try to parse the response for more details
            let responseData;
            try {
              responseData = await response.json();
            } catch (e) {
              responseData = { message: 'Rate limit exceeded' };
            }

            // Log the rate limit error
            console.warn(`Rate limit exceeded. Retry after ${retrySeconds} seconds. Circuit breaker: ${circuitBreakerStatus}`);

            // Show error message
            let rateLimitMsg = responseData.message || `Rate limit exceeded. Please wait ${retrySeconds} seconds before trying again.`;

            // Add circuit breaker information if applicable
            if (isCircuitOpen) {
              rateLimitMsg = `${rateLimitMsg} The API is temporarily unavailable due to excessive requests.`;
              // Store circuit breaker status in sessionStorage to prevent further requests
              try {
                sessionStorage.setItem('circuitBreakerOpen', 'true');
                sessionStorage.setItem('circuitBreakerResetTime', String(Date.now() + (retrySeconds * 1000)));
              } catch (e) {
                console.error('Failed to store circuit breaker status:', e);
              }
            }

            safeSetError(rateLimitMsg);
            if (onError && !silent) onError(rateLimitMsg);

            // Only show toast for manual refreshes
            if (!silent) {
              toast.error(
                isCircuitOpen ? 'API Temporarily Unavailable' : 'Rate Limit Exceeded',
                isCircuitOpen
                  ? `The API is temporarily unavailable due to excessive requests. Please wait ${retrySeconds} seconds.`
                  : `Too many requests. Please wait ${retrySeconds} seconds.`
              );
            }

            // For circuit breaker, don't retry automatically
            if (isCircuitOpen) {
              console.log('Circuit breaker is open. Not scheduling automatic retry.');
              safeSetLoading(false);
              return;
            }

            // Implement exponential backoff for auto-retries
            if (silent && fetchAttemptsRef.current < MAX_FETCH_ATTEMPTS) {
              const backoffDelay = Math.min(
                Math.pow(2, fetchAttemptsRef.current) * 1000,
                retrySeconds * 1000
              );

              console.log(`Scheduling retry in ${Math.round(backoffDelay/1000)} seconds (attempt ${fetchAttemptsRef.current + 1}/${MAX_FETCH_ATTEMPTS})`);

              // Schedule a retry with backoff
              setTimeout(() => {
                if (isMounted.current) {
                  // Check if circuit breaker has been opened in the meantime
                  try {
                    const circuitOpen = sessionStorage.getItem('circuitBreakerOpen') === 'true';
                    if (circuitOpen) {
                      console.log('Circuit breaker opened during retry wait. Cancelling retry.');
                      return;
                    }
                  } catch (e) {
                    // Ignore storage errors
                  }

                  fetchLogs(true);
                }
              }, backoffDelay);
            }

            safeSetLoading(false);
            return;
          }

          // For other error statuses
          let errorData;
          try {
            errorData = await response.json();
          } catch (e) {
            // If we can't parse the JSON, use a generic error message
            errorData = { error: `Failed to fetch logs (${response.status})` };
          }
          throw new Error(errorData.error || `Failed to fetch logs (${response.status})`);
        }

        let responseText = '';

        try {
          // First try to get the response text for debugging
          responseText = await response.text();

          // Then parse it as JSON
          try {
            const responseData = JSON.parse(responseText);
            // Log success but with less detail to prevent console spam
            if (!silent) {
              console.log('Response data received successfully');
            }

            // Get the logs array
            const logsArray = Array.isArray(responseData.logs) ? responseData.logs : [];

            // Process the logs
            processResponseData(logsArray);

            // Cache the response both in memory and sessionStorage
            const cacheData = {
              logs: logsArray,
              timestamp: now
            };

            apiCacheRef.current = cacheData;

            // Store in sessionStorage for persistence between page refreshes
            try {
              // Limit the number of logs stored in sessionStorage to reduce memory usage
              const logsToStore = logsArray.slice(0, 50); // Store only the first 50 logs
              const storageData = {
                logs: logsToStore,
                timestamp: now
              };

              sessionStorage.setItem(globalCacheKey, JSON.stringify(storageData));
            } catch (storageError) {
              console.error('Error storing in sessionStorage:', storageError);

              // If the error is a quota exceeded error, try to store with fewer logs
              if (storageError instanceof DOMException &&
                  (storageError.name === 'QuotaExceededError' ||
                   storageError.name === 'NS_ERROR_DOM_QUOTA_REACHED')) {
                try {
                  // Try with even fewer logs
                  const minimalLogsToStore = logsArray.slice(0, 20);
                  const minimalStorageData = {
                    logs: minimalLogsToStore,
                    timestamp: now
                  };
                  sessionStorage.setItem(globalCacheKey, JSON.stringify(minimalStorageData));
                } catch (e) {
                  console.error('Failed to store even minimal logs in sessionStorage:', e);
                }
              }
            }

            // Record successful fetch time
            lastSuccessfulFetch.current = now;

            // Reset fetch attempts counter after successful fetch
            fetchAttemptsRef.current = 0;

            // If we have user data in the response, add it to our cache
            if (responseData.user) {
              safeSetUserCache(prev => ({
                ...prev,
                [responseData.user.id]: {
                  name: responseData.user.name,
                  profileImage: responseData.user.profileImage
                }
              }));
            }
          } catch (jsonParseError) {
            console.error('Error parsing activity logs JSON:', jsonParseError);
            console.error('Raw response text:', responseText);
            throw new Error('Failed to parse activity logs data - invalid JSON response');
          }
        } catch (textError) {
          console.error('Error reading response text:', textError);
          throw new Error('Failed to read API response');
        }
      } catch (fetchError) {
        // Clear the timeout if there was an error
        clearTimeout(timeoutId);

        console.error('Fetch error:', fetchError);

        // Only try simplified fetch if we haven't exceeded max attempts
        if (!silent && fetchAttemptsRef.current < MAX_FETCH_ATTEMPTS) {
          try {
            const simpleResponse = await fetch(url, {
              credentials: 'include', // Include cookies for authentication
              cache: 'no-store'
            });
            if (simpleResponse.ok) {
              const simpleData = await simpleResponse.json();
              if (Array.isArray(simpleData.logs)) {
                processResponseData(simpleData.logs);
                return;
              }
            }
          } catch (simpleError) {
            console.error('Failed to fetch activity logs:', simpleError);
          }
        } else if (fetchAttemptsRef.current >= MAX_FETCH_ATTEMPTS) {
          console.warn(`Maximum fetch attempts (${MAX_FETCH_ATTEMPTS}) reached. Giving up.`);
        }

        throw fetchError;
      }
    } catch (error) {
      console.error('Error fetching activity logs:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      safeSetError(errorMessage);

      // Notify parent component if callback provided
      if (onError && !silent) {
        onError(errorMessage);
      }

      // Only show toast for manual refreshes and limit frequency
      if (!silent) {
        toast.error('Error', 'Failed to fetch activity logs');
      }
    } finally {
      // Release the request lock
      isRequestInProgressRef.current = false;

      // Reset loading state
      safeSetLoading(false);

      // Check if we need to activate rate limiting
      if (fetchAttemptsRef.current >= MAX_FETCH_ATTEMPTS) {
        console.warn(`Rate limiting activated: Too many failed attempts (${fetchAttemptsRef.current})`);

        // Set rate limiting
        rateLimitActiveRef.current = true;

        // Show error
        safeSetError('API calls are rate limited due to too many failed attempts. Please wait before trying again.');
      }
    }
  }, [userId, limit, toast, processResponseData, safeSetError, safeSetLoading, safeSetUserCache, safeSetLastRefresh, onError, globalCacheKey, authUser?.id]);

  // Initial data fetch on mount
  useEffect(() => {
    console.log("UserActivityLogs mounted, fetching initial data");
    
    // Add a small delay to ensure component is properly mounted before fetching
    const timer = setTimeout(() => {
      fetchLogs(true);
    }, 300);
    
    // Set up refresh interval
    let interval: NodeJS.Timeout | null = null;
    if (autoRefresh && refreshInterval) {
      interval = setInterval(() => {
        console.log("Auto-refreshing logs");
        fetchLogs(true);
      }, refreshInterval);
    }
    
    return () => {
      if (timer) clearTimeout(timer);
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh, refreshInterval, fetchLogs]);

  // Expose fetchLogs method to parent components via ref
  useImperativeHandle(ref, () => ({
    fetchLogs
  }));

  // Track auto-refresh interval ID
  const autoRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Setup auto-refresh if enabled with better throttling
  useEffect(() => {
    // Clear any existing timeout first
    if (autoRefreshIntervalRef.current) {
      clearTimeout(autoRefreshIntervalRef.current);
      autoRefreshIntervalRef.current = null;
      console.log('Cleared existing auto-refresh timeout');
    }

    // Return early if auto-refresh is disabled
    if (!autoRefresh) {
      console.log('Auto-refresh is disabled');
      return;
    }

    // Don't set up auto-refresh if there's an authentication error
    if (error && (error.includes('Authentication') || error.includes('session expired'))) {
      console.log('Auto-refresh disabled due to authentication error:', error);
      return;
    }

    // Check if circuit breaker is open
    try {
      const circuitOpen = sessionStorage.getItem('circuitBreakerOpen') === 'true';
      const circuitResetTimeStr = sessionStorage.getItem('circuitBreakerResetTime');

      if (circuitOpen && circuitResetTimeStr) {
        const circuitResetTime = parseInt(circuitResetTimeStr, 10);
        const now = Date.now();

        if (now < circuitResetTime) {
          // Circuit breaker is still open
          const remainingSeconds = Math.ceil((circuitResetTime - now) / 1000);
          console.log(`Auto-refresh disabled due to open circuit breaker. Resets in ${remainingSeconds}s`);

          // Schedule a check after the circuit breaker should reset
          const checkDelay = Math.max(circuitResetTime - now + 1000, 1000);
          setTimeout(() => {
            // Clear circuit breaker status if it's expired
            if (Date.now() >= circuitResetTime) {
              console.log('Circuit breaker reset time reached. Clearing status.');
              sessionStorage.removeItem('circuitBreakerOpen');
              sessionStorage.removeItem('circuitBreakerResetTime');
            }
          }, checkDelay);

          return;
        } else {
          // Circuit breaker has expired, clear it
          console.log('Circuit breaker has expired. Clearing status.');
          sessionStorage.removeItem('circuitBreakerOpen');
          sessionStorage.removeItem('circuitBreakerResetTime');
        }
      }
    } catch (e) {
      // Ignore storage errors
    }

    // Use a more conservative refresh interval - at least 10 minutes
    const actualInterval = Math.max(refreshInterval, 600000); // 10 minutes minimum (increased from 5)

    // For debugging
    if (refreshInterval < 600000) {
      console.log(`Auto-refresh interval increased from ${refreshInterval}ms to 600000ms to reduce API load`);
    }

    // Add jitter to prevent all clients refreshing at exactly the same time
    // This helps distribute server load
    const jitter = Math.floor(Math.random() * 60000); // Random delay up to 60 seconds (increased from 30)
    const intervalWithJitter = actualInterval + jitter;

    console.log(`Setting up auto-refresh with interval: ${Math.round(intervalWithJitter / 1000)}s (including ${Math.round(jitter / 1000)}s jitter)`);

    // For other types of errors, we can still try to refresh
    autoRefreshIntervalRef.current = setTimeout(() => {
      // Use setTimeout instead of setInterval to prevent overlapping requests
      const refreshData = () => {
        // Check if rate limiting is active
        if (rateLimitActiveRef.current) {
          console.warn('Auto-refresh skipped: Rate limiting is active');

          // Schedule next check with a longer delay
          if (autoRefresh && isMounted.current) {
            autoRefreshIntervalRef.current = setTimeout(refreshData, 60000); // Check again in 1 minute
          }
          return;
        }

        // Check if a request is already in progress
        if (isRequestInProgressRef.current) {
          console.warn('Auto-refresh skipped: Request already in progress');

          // Schedule next check with a short delay
          if (autoRefresh && isMounted.current) {
            autoRefreshIntervalRef.current = setTimeout(refreshData, 30000); // Check again in 30 seconds
          }
          return;
        }

        // Check if circuit breaker is open before making a request
        try {

          const circuitOpen = sessionStorage.getItem('circuitBreakerOpen') === 'true';
          if (circuitOpen) {
            console.log('Auto-refresh skipped: Circuit breaker is open');

            // Schedule next check with a longer delay
            if (autoRefresh && isMounted.current) {
              autoRefreshIntervalRef.current = setTimeout(refreshData, 60000); // Check again in 1 minute
            }
            return;
          }
        } catch (e) {
          // Ignore storage errors
        }

        // Check if user is still authenticated using auth context
        // We'll continue even if not authenticated, as we're using cookies for auth
        if (!isAuthenticated || !authUser) {
          console.log('Warning: User not authenticated in auth context, but continuing with cookies');
        }

        // Check if we've had too many consecutive errors
        if (fetchAttemptsRef.current >= MAX_FETCH_ATTEMPTS) {
          console.warn(`Auto-refresh paused due to ${fetchAttemptsRef.current} consecutive failed attempts`);

          // Reset fetch attempts after a cooling period
          setTimeout(() => {
            if (isMounted.current) {
              console.log('Resetting fetch attempts counter after cooling period');
              fetchAttemptsRef.current = 0;
            }
          }, 300000); // 5 minute cooling period

          return;
        }

        // Use silent refresh to avoid showing loading state and use caching
        fetchLogs(true)
          .then(() => {
            // Schedule next refresh only after current one completes
            if (autoRefresh && isMounted.current) {
              const nextInterval = Math.max(actualInterval, 600000); // At least 10 minutes
              autoRefreshIntervalRef.current = setTimeout(refreshData, nextInterval);
            }
          })
          .catch(err => {
            console.error('Error during auto-refresh:', err);
            // Still schedule next refresh, but with a longer delay if there was an error
            if (autoRefresh && isMounted.current) {
              const errorBackoffInterval = Math.max(actualInterval * 2, 1200000); // At least 20 minutes
              autoRefreshIntervalRef.current = setTimeout(refreshData, errorBackoffInterval);
            }
          });
      };

      // Start the refresh cycle
      refreshData();
    }, intervalWithJitter);

    // Clean up on unmount
    return () => {
      console.log('Cleaning up auto-refresh interval');
      if (autoRefreshIntervalRef.current) {
        clearTimeout(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }
    };
  }, [autoRefresh, refreshInterval, fetchLogs, safeSetError, error, authUser, isAuthenticated]);

  // Effect to fetch user info when logs change - no longer needed as we get user info from logs
  useEffect(() => {
    // Only fetch user info if we need to (this should be rare now)
    const missingUserInfo = logs.some(log => !log.user?.name && !userCache[log.userId]?.name);
    if (missingUserInfo) {
      fetchUserInfo();
    }
  }, [logs, fetchUserInfo, userCache]);

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (e) {
      return 'Unknown time';
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get icon for activity type
  const getActivityIcon = (type: string, action: string) => {
    if (type.toLowerCase() === 'auth') {
      if (action.toLowerCase() === 'signup') return <UserPlus className="h-4 w-4" />;
      if (action.toLowerCase() === 'login') return <LogIn className="h-4 w-4" />;
    }

    switch (type.toLowerCase()) {
      case 'admin':
        return <Settings className="h-4 w-4" />;
      case 'payment':
        return <MessageSquare className="h-4 w-4" />;
      case 'system':
        return <Server className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  // Get badge variant for activity type
  const getActivityBadgeVariant = (type: string, action?: string): "default" | "secondary" | "destructive" | "outline" => {
    if (type.toLowerCase() === 'auth') {
      if (action?.toLowerCase() === 'signup') return 'default';
      if (action?.toLowerCase() === 'login') return 'default';
    }

    switch (type.toLowerCase()) {
      case 'admin':
        return 'default';
      case 'payment':
        return 'secondary';
      case 'system':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  // Get user name from API response, then cache, then fallback
  const getUserName = (userId: string, log?: ActivityLog) => {
    // If log is provided, prefer userName directly from the log (from API)
    if (log?.userName && log.userName !== 'Unknown') {
      return log.userName;
    }

    // Then check if user info is in the logs array
    const userLog = logs.find(l => l.userId === userId && (l.userName !== 'Unknown' || l.user?.name));
    if (userLog?.userName && userLog.userName !== 'Unknown') {
      return userLog.userName;
    }
    if (userLog?.user?.name) {
      return userLog.user.name;
    }

    // Then check the cache
    return userCache[userId]?.name || 'Unknown User';
  };

  // Get user avatar from cache or fall back to placeholder
  const getUserAvatar = (userId: string) => {
    // First check if the user info is in the logs array
    const userFromLogs = logs.find(log => log.userId === userId && log.user?.profileImage);
    if (userFromLogs?.user?.profileImage) {
      return userFromLogs.user.profileImage;
    }

    return userCache[userId]?.profileImage || '';
  };

  // Add debugging for component state
  useEffect(() => {
    console.log("UserActivityLogs render state:", {
      isLoading,
      hasError: !!error,
      logsCount: logs.length,
      lastRefreshTime: lastRefresh ? formatRelativeTime(lastRefresh.toISOString()) : 'never'
    });
  }, [isLoading, error, logs.length, lastRefresh]);

  // Get user initials for the avatar fallback
  const getUserInitials = (userId: string, log?: ActivityLog) => {
    const name = getUserName(userId, log);
    if (name === 'Unknown User') return 'U';

    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Determine if a device is mobile based on user agent
  const isMobileDevice = (userAgent: string) => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  };

  // Format IP for display (may hide part for privacy if needed)
  const formatIp = (ip: string) => {
    if (!ip || ip === 'unknown') return 'unknown';

    // If you want to partially hide IP for privacy:
    // const parts = ip.split('.');
    // return parts.length === 4 ? `${parts[0]}.${parts[1]}.*.*` : ip;

    return ip;
  };

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader className="flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0 pb-3">
        <div className="min-w-0 flex-1">
          <CardTitle className="text-vista-light text-lg sm:text-xl truncate">User Activity Logs</CardTitle>
          <CardDescription className="text-xs sm:text-sm">
            <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
              <span>{userId ? 'Activity history for this user' : 'Recent user activities across the platform'}</span>
              {lastRefresh && (
                <span className="text-xs text-vista-light/50">
                  Last updated {formatRelativeTime(lastRefresh.toISOString())}
                </span>
              )}
            </div>
          </CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => fetchLogs(false)}
          disabled={isLoading}
          className="transition-all duration-200 hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30 active:scale-95 w-full sm:w-auto shrink-0"
        >
          <RefreshCw className={`mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        {error ? (
          <div className="p-4 text-center text-red-500">
            <AlertCircle className="h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-2" />
            <p className="text-sm sm:text-base break-words">{error}</p>
            {error.includes('Authentication') || error.includes('session expired') ? (
              <div className="mt-4 space-y-2">
                <p className="text-xs sm:text-sm text-vista-light/70">
                  Your session may have expired. Please try refreshing the page or signing in again.
                </p>
                <div className="flex flex-col sm:flex-row justify-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.location.reload()}
                    className="transition-all duration-200 hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30 active:scale-95 w-full sm:w-auto"
                  >
                    Refresh Page
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => window.location.href = '/auth/signin?redirect=/admin/activity'}
                    className="transition-all duration-200 hover:bg-vista-blue/90 active:scale-95 w-full sm:w-auto"
                  >
                    Sign In
                  </Button>
                </div>
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchLogs(false)}
                className="mt-2 transition-all duration-200 hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30 active:scale-95 w-full sm:w-auto"
              >
                Try Again
              </Button>
            )}
          </div>
        ) : (
          <div className="rounded-md border border-vista-light/10 h-[350px] sm:h-[400px] overflow-hidden">
            {/* Mobile-optimized layout */}
            <div className="block md:hidden h-full">
              <ScrollArea className="h-full admin-scroll-mobile">
                <div className="space-y-2 p-3">
                  {isLoading && logs.length === 0 ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <div key={index} className="admin-card-mobile">
                        <Skeleton className="h-3 w-16 mb-2" />
                        <Skeleton className="h-3 w-full mb-1" />
                        <Skeleton className="h-2 w-3/4" />
                      </div>
                    ))
                  ) : logs.length > 0 ? (
                    logs.map((log) => (
                      <div key={log.id} className="admin-card-mobile">
                        <div className="flex items-start justify-between mb-2 gap-2">
                          <Badge
                            variant={getActivityBadgeVariant(log.type, log.action)}
                            className="flex items-center gap-1 text-xs px-2 py-1 shrink-0"
                          >
                            {getActivityIcon(log.type, log.action)}
                            <span className="truncate max-w-[80px]">{log.action}</span>
                          </Badge>
                          <span className="text-xs text-vista-light/50 shrink-0">
                            {formatRelativeTime(log.timestamp)}
                          </span>
                        </div>

                        {!userId && (
                          <div className="flex items-center gap-2 mb-2 min-w-0">
                            <Avatar className="h-5 w-5 shrink-0">
                              <AvatarImage src={getUserAvatar(log.userId)} alt={getUserName(log.userId, log)} />
                              <AvatarFallback className="text-xs bg-vista-blue/20 text-vista-blue">
                                {getUserInitials(log.userId, log)}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-xs font-medium text-vista-light truncate">
                              {getUserName(log.userId, log)}
                            </span>
                          </div>
                        )}

                        <p className="text-xs text-vista-light/90 break-words mb-2 leading-relaxed">{log.details}</p>

                        <div className="text-xs text-vista-light/70">
                          <div className="flex items-center justify-between">
                            <span className="truncate">
                              {isMobileDevice(log.userAgent) ? 'Mobile' : 'Desktop'}
                            </span>
                            <span className="text-vista-light/50 truncate ml-2">
                              {formatIp(log.ipAddress)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-vista-light/50">
                      <AlertCircle className="h-6 w-6 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No activity logs available</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Desktop table layout */}
            <div className="hidden md:block h-full">
              <ScrollArea className="h-full admin-scrollbar">
                <Table>
                  <TableHeader className="sticky top-0 bg-background z-10">
                    <TableRow>
                      <TableHead>Time</TableHead>
                      {!userId && <TableHead>User</TableHead>}
                      <TableHead>Activity</TableHead>
                      <TableHead className="w-[40%]">Details</TableHead>
                      <TableHead>Device/IP</TableHead>
                    </TableRow>
                  </TableHeader>
              <TableBody>
                {isLoading && logs.length === 0 ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      {!userId && <TableCell><Skeleton className="h-4 w-32" /></TableCell>}
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                    </TableRow>
                  ))
                ) : logs.length > 0 ? (
                  logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="whitespace-nowrap">
                        <div className="flex flex-col">
                          <span className="text-xs text-vista-light/70" title={formatDate(log.timestamp)}>
                            {formatRelativeTime(log.timestamp)}
                          </span>
                        </div>
                      </TableCell>

                      {!userId && (
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-7 w-7">
                              <AvatarImage src={getUserAvatar(log.userId)} alt={getUserName(log.userId, log)} />
                              <AvatarFallback className="text-xs bg-vista-blue/20 text-vista-blue">
                                {getUserInitials(log.userId, log)}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-sm font-medium truncate max-w-[130px]" title={getUserName(log.userId, log)}>
                              {getUserName(log.userId, log)}
                            </span>
                          </div>
                        </TableCell>
                      )}

                      <TableCell>
                        <Badge
                          variant={getActivityBadgeVariant(log.type, log.action)}
                          className="flex items-center gap-1"
                        >
                          {getActivityIcon(log.type, log.action)}
                          {log.action}
                        </Badge>
                      </TableCell>

                      <TableCell className="text-sm text-vista-light/90 font-medium">
                        {log.details}
                      </TableCell>

                      <TableCell className="text-xs text-vista-light/70">
                        <div className="flex flex-col">
                          <span title={log.userAgent}>
                            {isMobileDevice(log.userAgent) ? 'Mobile' : 'Desktop'}
                          </span>
                          <span className="text-vista-light/50" title={log.ipAddress}>
                            {formatIp(log.ipAddress)}
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={!userId ? 5 : 4} className="text-center py-10 text-vista-light/50">
                      <div className="flex flex-col items-center gap-2">
                        <p>No activity logs found</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => fetchLogs(false)}
                          className="mt-2 transition-all duration-200 hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30 active:scale-95"
                        >
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Refresh
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </ScrollArea>
        </div>
      </div>
    )}
      </CardContent>
    </Card>
  );
});

// Add display name to the component
UserActivityLogs.displayName = 'UserActivityLogs';

// Export the component
export default UserActivityLogs;
