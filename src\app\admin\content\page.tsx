'use client';

import React, { useState } from 'react';
import {
  Search,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  Film,
  Tv,
  Calendar,
  Star,
  Clock,
  RefreshCw,
  PlusCircle,
  RotateCw,
  Folder
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useContentList } from '@/hooks/useAdminData';
import { toast } from '@/components/ui/use-toast';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle } from 'lucide-react';
import ContentFormModal from '@/components/admin/ContentFormModal';



interface ContentApiItem {
  id: string;
  title: string;
  type: "movie" | "show" | "tv";  // Include "tv" for compatibility with ContentFormModal
  tmdbId: string;
  imdbId?: string;
  posterPath: string;
  backdropPath?: string;
  overview: string;  // Required by ContentFormModal
  year?: string;
  genres: string[];
  releaseDate?: string;
  runtime?: number;
  rating?: number;
  status: "published" | "draft" | "archived";
  featured?: boolean;
  trending?: boolean;
  views?: number;
  createdAt: string;
  seasons?: number;
  episodes?: number;
}

// Define a type for the content form modal
interface ContentFormItem {
  id: string;
  title: string;
  type: "movie" | "tv";
  overview: string;
  releaseDate?: string;
  posterPath?: string;
  backdropPath?: string;
  genres?: string[];
  status?: string;
  featured?: boolean;
}

interface SyncResult {
  inserted: number;
  updated: number;
  total: number;
  message?: string;
}

export default function ContentManagementPage() {
  const router = useRouter();

  // Admin access is now protected by middleware and layout

  // State for tabs, search, and filters
  const [activeTab, setActiveTab] = useState('movies');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [genreFilter, setGenreFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncResult, setSyncResult] = useState<SyncResult | null>(null);
  const [isContentModalOpen, setIsContentModalOpen] = useState(false);
  const [editingContent, setEditingContent] = useState<ContentFormItem | undefined>(undefined);

  // Determine content type based on active tab
  const contentType = activeTab === 'movies' ? 'movie' : 'show';

  // Fetch content data
  const {
    data: contentData,
    isLoading,
    refetch
  } = useContentList(
    currentPage,
    10,
    searchQuery,
    contentType,
    statusFilter !== 'all' ? statusFilter : '',
    genreFilter !== 'all' ? genreFilter : ''
  );

  // Map the API response to typed content items with correct types
  const content = React.useMemo(() => {
    if (!contentData?.content) return [];

    // Use a type assertion to treat the API response with the correct shape
    return contentData.content.map(item => {
      // Create a properly typed object with all expected properties
      // Use type assertion to handle the API response
      const apiItem = item as unknown as {
        id: string;
        title: string;
        type: string;
        tmdbId: string;
        imdbId?: string;
        posterPath: string;
        backdropPath?: string;
        overview?: string;
        year?: string;
        genres?: string[];
        releaseDate?: string;
        runtime?: number;
        rating?: number;
        status: string;
        featured?: boolean;
        trending?: boolean;
        views?: number;
        createdAt: string;
        seasons?: number;
        episodes?: number;
      };

      const contentItem: ContentApiItem = {
        id: apiItem.id,
        title: apiItem.title,
        type: (apiItem.type === 'show' ? 'tv' : apiItem.type) as "movie" | "show" | "tv",
        tmdbId: apiItem.tmdbId,
        imdbId: apiItem.imdbId,
        posterPath: apiItem.posterPath,
        backdropPath: apiItem.backdropPath,
        overview: apiItem.overview || '', // Provide a default empty string
        year: apiItem.year,
        genres: apiItem.genres || [],
        releaseDate: apiItem.releaseDate,
        runtime: apiItem.runtime,
        rating: apiItem.rating,
        status: apiItem.status as "published" | "draft" | "archived",
        featured: apiItem.featured,
        trending: apiItem.trending,
        views: apiItem.views,
        createdAt: apiItem.createdAt,
        seasons: apiItem.seasons,
        episodes: apiItem.episodes
      };
      return contentItem;
    });
  }, [contentData]);

  // Extract pagination info
  const pagination = contentData?.pagination || { page: 1, limit: 10, total: 0, pages: 1 };

  // Get all unique genres from content
  const allGenres = [...new Set(
    content.flatMap(item => item.genres || [])
  )].sort();

  // Handle search
  const handleSearch = () => {
    refetch();
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setCurrentPage(1); // Reset to first page when changing tabs
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle filter change
  const handleFilterChange = (type: 'status' | 'genre', value: string) => {
    if (type === 'status') {
      setStatusFilter(value);
    } else {
      setGenreFilter(value);
    }
    setCurrentPage(1); // Reset to first page when changing filters
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format duration for display
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  // Handle content deletion
  const handleDeleteContent = async (id: string) => {
    if (confirm('Are you sure you want to delete this content? This action cannot be undone.')) {
      try {
        const response = await fetch(`/api/admin/content/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to delete content (${response.status})`);
        }

        toast({
          title: 'Content deleted',
          description: 'The content has been successfully deleted.',
          variant: 'default'
        });

        // Refresh the content list
        refetch();
      } catch (error) {
        console.error('Error deleting content:', error);
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to delete content',
          variant: 'destructive'
        });
      }
    }
  };

  // Handle status change
  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/content/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus }),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update content status (${response.status})`);
      }

      toast({
        title: 'Status updated',
        description: `Content status has been updated to ${newStatus}.`,
        variant: 'default'
      });

      // Refresh the content list
      refetch();
    } catch (error) {
      console.error('Error updating content status:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update content status',
        variant: 'destructive'
      });
    }
  };

  // Handle sync with TMDB
  const handleSync = async () => {
    if (isSyncing) return;

    setIsSyncing(true);
    setSyncResult(null);

    try {
      const response = await fetch('/api/admin/content?action=sync', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to sync content');
      }

      setSyncResult({
        inserted: data.inserted || 0,
        updated: data.updated || 0,
        total: data.total || 0,
        message: data.message
      });

      // Refresh content list
      refetch();
    } catch (error) {
      console.error('Error syncing content:', error);
      toast({
        title: 'Sync Failed',
        description: error instanceof Error ? error.message : 'Failed to sync content',
        variant: 'destructive'
      });
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle adding new content
  const handleAddContent = () => {
    setEditingContent(undefined);
    setIsContentModalOpen(true);
  };

  // Convert ContentApiItem to the format expected by ContentFormModal
  const convertToFormContent = (content: ContentApiItem): ContentFormItem => {
    return {
      id: content.id,
      title: content.title,
      type: content.type === 'show' ? 'tv' : content.type as 'movie' | 'tv',
      overview: content.overview,
      releaseDate: content.releaseDate,
      posterPath: content.posterPath,
      backdropPath: content.backdropPath,
      genres: content.genres,
      status: content.status,
      featured: content.featured
    };
  };

  // Handle editing content
  const handleEditContent = (content: ContentApiItem) => {
    setEditingContent(convertToFormContent(content));
    setIsContentModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsContentModalOpen(false);
    setEditingContent(undefined);
  };



  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex flex-col gap-3">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-vista-light">Content Management</h1>
            <p className="text-vista-light/70">
              Manage movies, TV shows, and other content
            </p>
          </div>

          {/* Action Buttons - Mobile Optimized */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <div className="flex gap-2 flex-1">
              <Button
                onClick={() => router.push('/admin/content/categories')}
                variant="outline"
                size="sm"
                className="flex-1 sm:flex-none gap-2"
              >
                <Folder className="h-4 w-4" />
                <span className="hidden sm:inline">Categories & Tags</span>
                <span className="sm:hidden">Categories</span>
              </Button>
              <Button
                onClick={() => router.push('/admin/content/featured')}
                variant="outline"
                size="sm"
                className="flex-1 sm:flex-none gap-2"
              >
                <Star className="h-4 w-4" />
                <span className="hidden sm:inline">Featured Content</span>
                <span className="sm:hidden">Featured</span>
              </Button>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleSync}
                disabled={isSyncing}
                size="sm"
                variant="outline"
                className="flex-1 sm:flex-none gap-2"
              >
                <RotateCw className={`h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`} />
                <span className="hidden sm:inline">{isSyncing ? 'Syncing...' : 'Sync with TMDB'}</span>
                <span className="sm:hidden">{isSyncing ? 'Syncing...' : 'Sync'}</span>
              </Button>
              <Button
                onClick={handleAddContent}
                size="sm"
                className="flex-1 sm:flex-none gap-2"
              >
                <PlusCircle className="h-4 w-4" />
                <span className="hidden sm:inline">Add Content</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Sync result alert */}
      {syncResult && (
        <Alert variant={syncResult.inserted > 0 ? "default" : "destructive"}>
          {syncResult.inserted > 0 ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertCircle className="h-4 w-4" />
          )}
          <AlertTitle>
            {syncResult.inserted > 0 ? "Sync Completed" : "Sync Failed"}
          </AlertTitle>
          <AlertDescription>
            {syncResult.inserted > 0 ? (
              `Successfully synced ${syncResult.total} content items. Added ${syncResult.inserted} new items and updated ${syncResult.updated} existing items.`
            ) : syncResult.message}
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="text-vista-light">Content Library</CardTitle>
          <CardDescription>
            Manage your streaming content
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 mb-6">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/50 h-4 w-4" />
              <Input
                placeholder="Search content..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>

            {/* Filters and Actions */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="flex gap-2 flex-1">
                <Select value={statusFilter} onValueChange={(value) => handleFilterChange('status', value)}>
                  <SelectTrigger className="flex-1 sm:w-[140px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={genreFilter} onValueChange={(value) => handleFilterChange('genre', value)}>
                  <SelectTrigger className="flex-1 sm:w-[140px]">
                    <SelectValue placeholder="Genre" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Genres</SelectItem>
                    {allGenres.map(genre => (
                      <SelectItem key={genre} value={genre}>{genre}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSearch}
                  disabled={isLoading}
                  className="flex-1 sm:flex-none"
                >
                  <Search className="mr-2 h-4 w-4" />
                  <span className="hidden sm:inline">Search</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetch()}
                  disabled={isLoading}
                  className="flex-1 sm:flex-none"
                >
                  <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                  <span className="hidden sm:inline">{isLoading ? 'Loading...' : 'Refresh'}</span>
                </Button>
              </div>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="movies" className="flex items-center">
                <Film className="mr-2 h-4 w-4" />
                Movies
              </TabsTrigger>
              <TabsTrigger value="shows" className="flex items-center">
                <Tv className="mr-2 h-4 w-4" />
                TV Shows
              </TabsTrigger>
            </TabsList>

            <TabsContent value="movies">
              {isLoading ? (
                <div className="text-center py-12 text-vista-light/70">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p>Loading movies...</p>
                </div>
              ) : content.filter(item => item.type === 'movie').length > 0 ? (
                <div className="grid gap-4 md:gap-6">
                  {content.filter(item => item.type === 'movie').map((movie) => (
                    <div
                      key={movie.id}
                      className="bg-vista-dark/50 border border-vista-light/10 rounded-lg p-4 md:p-6 hover:bg-vista-dark/70 transition-colors"
                    >
                      <div className="flex flex-col sm:flex-row gap-4">
                        {/* Poster */}
                        <div className="flex-shrink-0 mx-auto sm:mx-0">
                          <div className="h-32 w-24 sm:h-40 sm:w-28 rounded-lg overflow-hidden bg-vista-dark-lighter relative">
                            {movie.posterPath && (
                              <Image
                                src={`https://image.tmdb.org/t/p/w185${movie.posterPath}`}
                                alt={movie.title}
                                className="object-cover"
                                fill
                                sizes="(max-width: 640px) 96px, 112px"
                              />
                            )}
                          </div>
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0 space-y-3">
                          {/* Title and Status */}
                          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                            <div className="min-w-0">
                              <h3 className="text-lg font-semibold text-vista-light truncate">
                                {movie.title}
                              </h3>
                              <p className="text-sm text-vista-light/60">ID: {movie.id}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={
                                  movie.status === 'published'
                                    ? 'default'
                                    : movie.status === 'draft'
                                      ? 'secondary'
                                      : 'outline'
                                }
                                className="capitalize"
                              >
                                {movie.status}
                              </Badge>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleEditContent(movie)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Movie
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuLabel>Status</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => handleStatusChange(movie.id, 'published')}>
                                    Set as Published
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleStatusChange(movie.id, 'draft')}>
                                    Set as Draft
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleStatusChange(movie.id, 'archived')}>
                                    Archive
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    className="text-red-500 focus:text-red-500"
                                    onClick={() => handleDeleteContent(movie.id)}
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete Movie
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>

                          {/* Movie Details */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-vista-light/70" />
                              <span className="text-vista-light/80">
                                {movie.releaseDate ? formatDate(movie.releaseDate) : movie.year || 'N/A'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <span className="text-vista-light/80">
                                {movie.rating || 'N/A'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-vista-light/70" />
                              <span className="text-vista-light/80">
                                {movie.runtime ? formatDuration(movie.runtime) : 'N/A'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Eye className="h-4 w-4 text-vista-light/70" />
                              <span className="text-vista-light/80">
                                {movie.views || 0} views
                              </span>
                            </div>
                          </div>

                          {/* Genres */}
                          {movie.genres && movie.genres.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {movie.genres.map((genre, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {genre}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-vista-light/70">
                  <Film className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No movies found</p>
                  <p className="text-sm">No movies match your current search criteria.</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="shows">
              {isLoading ? (
                <div className="text-center py-12 text-vista-light/70">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p>Loading TV shows...</p>
                </div>
              ) : content.filter(item => item.type === 'show').length > 0 ? (
                <div className="grid gap-4 md:gap-6">
                  {content.filter(item => item.type === 'show').map((show) => (
                    <div
                      key={show.id}
                      className="bg-vista-dark/50 border border-vista-light/10 rounded-lg p-4 md:p-6 hover:bg-vista-dark/70 transition-colors"
                    >
                      <div className="flex flex-col sm:flex-row gap-4">
                        {/* Poster */}
                        <div className="flex-shrink-0 mx-auto sm:mx-0">
                          <div className="h-32 w-24 sm:h-40 sm:w-28 rounded-lg overflow-hidden bg-vista-dark-lighter relative">
                            {show.posterPath && (
                              <Image
                                src={`https://image.tmdb.org/t/p/w185${show.posterPath}`}
                                alt={show.title}
                                className="object-cover"
                                fill
                                sizes="(max-width: 640px) 96px, 112px"
                              />
                            )}
                          </div>
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0 space-y-3">
                          {/* Title and Status */}
                          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                            <div className="min-w-0">
                              <h3 className="text-lg font-semibold text-vista-light truncate">
                                {show.title}
                              </h3>
                              <p className="text-sm text-vista-light/60">ID: {show.id}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={
                                  show.status === 'published'
                                    ? 'default'
                                    : show.status === 'draft'
                                      ? 'secondary'
                                      : 'outline'
                                }
                                className="capitalize"
                              >
                                {show.status}
                              </Badge>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleEditContent(show)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Show
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuLabel>Status</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => handleStatusChange(show.id, 'published')}>
                                    Set as Published
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleStatusChange(show.id, 'draft')}>
                                    Set as Draft
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleStatusChange(show.id, 'archived')}>
                                    Archive
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    className="text-red-500 focus:text-red-500"
                                    onClick={() => handleDeleteContent(show.id)}
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete Show
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>

                          {/* Show Details */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-vista-light/70" />
                              <span className="text-vista-light/80">
                                {show.releaseDate ? formatDate(show.releaseDate) : show.year || 'N/A'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <span className="text-vista-light/80">
                                {show.rating || 'N/A'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Tv className="h-4 w-4 text-vista-light/70" />
                              <span className="text-vista-light/80">
                                {show.seasons || 'N/A'} seasons
                                {show.episodes && ` (${show.episodes} eps)`}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Eye className="h-4 w-4 text-vista-light/70" />
                              <span className="text-vista-light/80">
                                {show.views || 0} views
                              </span>
                            </div>
                          </div>

                          {/* Genres */}
                          {show.genres && show.genres.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {show.genres.map((genre, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {genre}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-vista-light/70">
                  <Tv className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No TV shows found</p>
                  <p className="text-sm">No TV shows match your current search criteria.</p>
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="mt-6 space-y-4">
              {/* Results Info */}
              <div className="text-center sm:text-left">
                <p className="text-sm text-vista-light/70">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} items
                </p>
              </div>

              {/* Pagination Controls */}
              <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-between gap-4">
                {/* Previous Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1 || isLoading}
                  className="w-full sm:w-auto"
                >
                  Previous
                </Button>

                {/* Page Numbers - Mobile Optimized */}
                <div className="flex items-center gap-1 overflow-x-auto max-w-full">
                  {Array.from({ length: pagination.pages }, (_, i) => i + 1)
                    .filter(page => {
                      // On mobile, show fewer pages
                      const isMobile = typeof window !== 'undefined' && window.innerWidth < 640;
                      if (isMobile) {
                        return page === 1 ||
                               page === pagination.pages ||
                               Math.abs(page - pagination.page) <= 0;
                      }
                      // On desktop, show more pages
                      return page === 1 ||
                             page === pagination.pages ||
                             Math.abs(page - pagination.page) <= 1;
                    })
                    .map((page, index, array) => {
                      // Add ellipsis between non-consecutive pages
                      const showEllipsisBefore = index > 0 && array[index - 1] !== page - 1;
                      return (
                        <div key={page} className="flex items-center">
                          {showEllipsisBefore && (
                            <span className="px-2 text-vista-light/50 text-sm">...</span>
                          )}
                          <Button
                            variant={pagination.page === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                            disabled={isLoading}
                            className="min-w-[40px] h-9"
                          >
                            {page}
                          </Button>
                        </div>
                      );
                    })}
                </div>

                {/* Next Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.pages || isLoading}
                  className="w-full sm:w-auto"
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Content Form Modal */}
      <ContentFormModal
        isOpen={isContentModalOpen}
        onClose={handleModalClose}
        onSuccess={refetch}
        editContent={editingContent}
      />
    </div>
  );
}
